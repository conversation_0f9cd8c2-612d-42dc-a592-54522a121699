import { useSidebarStore } from '@/stores/sidebarStore'
import { fireEvent, render, screen } from '@testing-library/react'
import { SidebarToggleButton } from '../SidebarToggleButton'

// Mock the sidebar store
jest.mock('@/stores/sidebarStore')
const mockUseSidebarStore = useSidebarStore as jest.MockedFunction<typeof useSidebarStore>

describe('SidebarToggleButton', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders nothing when sidebar is expanded', () => {
    mockUseSidebarStore.mockReturnValue({
      isCollapsed: false,
      activeTab: 'Blocks',
      searchQuery: '',
      toggleCollapsed: jest.fn(),
      setActiveTab: jest.fn(),
      setSearchQuery: jest.fn(),
      collapse: jest.fn(),
      expand: jest.fn(),
    })

    const { container } = render(<SidebarToggleButton />)
    expect(container.firstChild).toBeNull()
  })

  it('renders toggle button when sidebar is collapsed', () => {
    const mockExpand = jest.fn()
    mockUseSidebarStore.mockReturnValue({
      isCollapsed: true,
      activeTab: 'Blocks',
      searchQuery: '',
      toggleCollapsed: jest.fn(),
      setActiveTab: jest.fn(),
      setSearchQuery: jest.fn(),
      collapse: jest.fn(),
      expand: mockExpand,
    })

    const { container } = render(<SidebarToggleButton />)

    // Check that toggle element is rendered
    const toggleElement = container.querySelector('div[class*="fixed"]')
    expect(toggleElement).toBeInTheDocument()

    // Check that PanelRight icon is rendered
    expect(toggleElement?.querySelector('svg')).toBeInTheDocument()
  })

  it('calls expand when clicked', () => {
    const mockExpand = jest.fn()
    mockUseSidebarStore.mockReturnValue({
      isCollapsed: true,
      activeTab: 'Blocks',
      searchQuery: '',
      toggleCollapsed: jest.fn(),
      setActiveTab: jest.fn(),
      setSearchQuery: jest.fn(),
      collapse: jest.fn(),
      expand: mockExpand,
    })

    const { container } = render(<SidebarToggleButton />)

    const toggleElement = container.querySelector('div[class*="fixed"]')
    fireEvent.click(toggleElement!)

    expect(mockExpand).toHaveBeenCalledTimes(1)
  })

  it('has correct positioning and styling classes', () => {
    mockUseSidebarStore.mockReturnValue({
      isCollapsed: true,
      activeTab: 'Blocks',
      searchQuery: '',
      toggleCollapsed: jest.fn(),
      setActiveTab: jest.fn(),
      setSearchQuery: jest.fn(),
      collapse: jest.fn(),
      expand: jest.fn(),
    })

    const { container } = render(<SidebarToggleButton />)

    const toggleElement = container.querySelector('div[class*="fixed"]')
    expect(toggleElement).toHaveClass('fixed', 'left-[90px]', 'bottom-[18px]')
    expect(toggleElement).toHaveClass('rounded-md', 'h-8', 'w-8')
    expect(toggleElement).toHaveClass('text-muted-foreground', 'hover:bg-gray-200', 'dark:hover:bg-gray-700')
  })
})
