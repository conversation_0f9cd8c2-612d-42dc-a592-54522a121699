import { render, screen } from '@testing-library/react'
import { LeftSidebar } from '../LeftSidebar'
import { useSidebarStore } from '@/stores/sidebarStore'

// Mock the sidebar store
jest.mock('@/stores/sidebarStore')
const mockUseSidebarStore = useSidebarStore as jest.MockedFunction<typeof useSidebarStore>

describe('LeftSidebar', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders nothing when collapsed', () => {
    mockUseSidebarStore.mockReturnValue({
      isCollapsed: true,
      activeTab: 'Blocks',
      searchQuery: '',
      toggleCollapsed: jest.fn(),
      setActiveTab: jest.fn(),
      setSearchQuery: jest.fn(),
      collapse: jest.fn(),
      expand: jest.fn(),
    })

    const { container } = render(<LeftSidebar />)
    expect(container.firstChild).toBeNull()
  })

  it('renders sidebar content when expanded', () => {
    mockUseSidebarStore.mockReturnValue({
      isCollapsed: false,
      activeTab: 'Blocks',
      searchQuery: '',
      toggleCollapsed: jest.fn(),
      setActiveTab: jest.fn(),
      setSearchQuery: jest.fn(),
      collapse: jest.fn(),
      expand: jest.fn(),
    })

    render(<LeftSidebar />)
    
    // Check that search input is rendered
    expect(screen.getByPlaceholderText('Search blocks and tools')).toBeInTheDocument()
    
    // Check that tabs are rendered
    expect(screen.getByText('Blocks')).toBeInTheDocument()
    expect(screen.getByText('Tools')).toBeInTheDocument()
    
    // Check that some blocks are rendered
    expect(screen.getByText('Agent')).toBeInTheDocument()
    expect(screen.getByText('API')).toBeInTheDocument()
  })

  it('filters blocks based on search query', () => {
    mockUseSidebarStore.mockReturnValue({
      isCollapsed: false,
      activeTab: 'Blocks',
      searchQuery: 'agent',
      toggleCollapsed: jest.fn(),
      setActiveTab: jest.fn(),
      setSearchQuery: jest.fn(),
      collapse: jest.fn(),
      expand: jest.fn(),
    })

    render(<LeftSidebar />)
    
    // Should show Agent block
    expect(screen.getByText('Agent')).toBeInTheDocument()
    
    // Should not show API block (doesn't match search)
    expect(screen.queryByText('API')).not.toBeInTheDocument()
  })
})
