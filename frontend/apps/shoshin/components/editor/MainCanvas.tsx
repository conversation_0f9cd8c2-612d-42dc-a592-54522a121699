"use client"

import {
    Background,
    Controls,
    MarkerType,
    MiniMap,
    ReactFlow,
    ReactFlowProvider,
    applyEdgeChanges,
    applyNodeChanges,
    useReactFlow,
    type Node,
    type OnConnect,
    type OnSelectionChangeParams,
} from "@xyflow/react"
import "@xyflow/react/dist/style.css"
import { useCallback, useEffect, useRef, useState } from "react"
import { useKeyboardShortcuts } from "../../hooks/useKeyboardShortcuts"
import { useToast } from "../../hooks/useToast"
import { applyAutoLayoutSmooth, createDebouncedAutoLayout } from "../../lib/autoLayout"
import { useEditorStore } from "../../stores/editorStore"
import { useSettingsStore } from "../../stores/settingsStore"
import { useSidebarStore } from "../../stores/sidebarStore"
import { ToastContainer } from "../ui/Toast"
import { CustomNode } from "./CustomNode"
import { NodeSettingsModal } from "./NodeSettingsModal"
import { ShoshinEdge } from "./ShoshinEdge"

const nodeTypes = {
  custom: CustomNode,
}

const edgeTypes = {
  shoshin: ShoshinEdge,
}



function MainCanvasInner() {
  const reactFlowWrapper = useRef<HTMLDivElement>(null)
  const [reactFlowInstance, setReactFlowInstance] = useState<unknown>(null)
  const { fitView, screenToFlowPosition } = useReactFlow()

  // Use Zustand stores for state management
  const {
    nodes,
    edges,
    setNodes,
    setEdges,
    updateSelection,
    addNode,
    connectNodes,
    updateCursorPosition,
    nodeSettingsModal,
    setNodeSettingsModal
  } = useEditorStore()

  // Use settings store for editor preferences
  const { editor: editorSettings } = useSettingsStore()
  const { isCollapsed } = useSidebarStore()

  // Initialize hooks
  const { toasts, removeToast } = useToast()

  // Enable keyboard shortcuts
  useKeyboardShortcuts()

  // ReactFlow change handlers
  const onNodesChange = useCallback((changes: Parameters<typeof applyNodeChanges>[0]) => {
    setNodes(applyNodeChanges(changes, nodes))
  }, [nodes, setNodes])

  const onEdgesChange = useCallback((changes: Parameters<typeof applyEdgeChanges>[0]) => {
    setEdges(applyEdgeChanges(changes, edges))
  }, [edges, setEdges])

  // Selection change handler
  const onSelectionChange = useCallback((params: OnSelectionChangeParams) => {
    updateSelection(params.nodes, params.edges)
  }, [updateSelection])





  // Enhanced auto layout function with smooth animations
  const applyAutoLayout = useCallback(() => {
    if (nodes.length === 0) return

    applyAutoLayoutSmooth(
      nodes,
      edges,
      setNodes,
      fitView,
      {
        horizontalSpacing: 360,
        verticalSpacing: 240,
        startX: 150,
        startY: 150,
        animationDuration: 500,
        handleOrientation: 'horizontal',
        minimizeEdgeCrossings: true,
        edgeCrossingIterations: 3,
        dynamicSpacing: true,
        edgeNodeSpacing: 40, // Increased to 40px for much better visual separation
        edgeNodeCollisionIterations: 15, // Many more iterations for thorough collision resolution
        edgeEdgeSpacing: 10, // Minimum 10px distance between edges with different source/target nodes
        edgeEdgeCollisionIterations: 5, // Iterations for edge-edge collision resolution
        onComplete: (finalPositions) => {
          console.log('Auto layout completed', {
            nodesPositioned: finalPositions.size,
            orientation: 'horizontal',
            edgeCrossingMinimizationEnabled: true,
            edgeNodeSpacingEnabled: true,
            edgeEdgeSpacingEnabled: true
          })
        }
      }
    )
  }, [nodes, edges, setNodes, fitView])

  // Create debounced auto layout to prevent rapid triggering
  const debouncedAutoLayout = useCallback(
    createDebouncedAutoLayout(applyAutoLayout),
    [applyAutoLayout]
  )

  // Listen for auto layout events with debouncing
  useEffect(() => {
    let cleanup: (() => void) | null = null

    const handleAutoLayout = () => {
      if (cleanup) cleanup()
      cleanup = debouncedAutoLayout()
    }

    window.addEventListener('trigger-auto-layout', handleAutoLayout)
    return () => {
      window.removeEventListener('trigger-auto-layout', handleAutoLayout)
      if (cleanup) cleanup()
    }
  }, [debouncedAutoLayout])

  // Auto-apply layout when editor is loaded/refreshed if nodes exist
  const [hasAutoLayoutApplied, setHasAutoLayoutApplied] = useState(false)

  useEffect(() => {
    // Only run this effect once when the component mounts and ReactFlow is ready
    // Check if we have nodes, ReactFlow instance is ready, and we haven't applied auto-layout yet
    if (nodes.length > 0 && reactFlowInstance && !hasAutoLayoutApplied) {
      // Add a small delay to ensure ReactFlow is fully initialized
      const timeoutId = setTimeout(() => {
        console.log('Auto-applying layout on editor load/refresh with', nodes.length, 'nodes')
        applyAutoLayout()
        setHasAutoLayoutApplied(true)
      }, 100)

      return () => clearTimeout(timeoutId)
    }
  }, [nodes.length, reactFlowInstance, hasAutoLayoutApplied, applyAutoLayout])

  const onConnect: OnConnect = useCallback(
    (params) => {
      connectNodes(params)
    },
    [connectNodes]
  )

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = "move"
  }, [])

  // Get viewport center for better default positioning
  const getViewportCenter = useCallback(() => {
    if (screenToFlowPosition && reactFlowWrapper.current) {
      const reactFlowBounds = reactFlowWrapper.current.getBoundingClientRect()
      const centerX = reactFlowBounds.left + reactFlowBounds.width / 2
      const centerY = reactFlowBounds.top + reactFlowBounds.height / 2

      const position = screenToFlowPosition({
        x: centerX,
        y: centerY,
      })

      return position || { x: 400, y: 300 }
    }
    return { x: 400, y: 300 }
  }, [screenToFlowPosition])

  // Track mouse position for paste functionality
  const onMouseMove = useCallback((event: React.MouseEvent) => {
    if (screenToFlowPosition) {
      // Use the hook's screenToFlowPosition which properly handles all transformations
      const position = screenToFlowPosition({
        x: event.clientX,
        y: event.clientY,
      })

      if (position) {
        updateCursorPosition(position)
        // Debug: Log cursor position updates occasionally
        if (Math.random() < 0.01) { // Log ~1% of mouse moves to avoid spam
          console.log('🖱️ Cursor position updated:', position, 'zoom:', (reactFlowInstance as any)?.getViewport?.().zoom)
        }
      }
    }
  }, [screenToFlowPosition, reactFlowInstance, updateCursorPosition])

  // Helper function to find the closest node to a given position
  const findClosestNode = useCallback(
    (newNodePosition: { x: number; y: number }): Node | null => {
      const MAX_AUTO_CONNECT_DISTANCE = 800; // Maximum distance for auto-connection

      const existingNodes = nodes
        .map((node) => ({
          ...node,
          distance: Math.sqrt(
            (node.position.x - newNodePosition.x) ** 2 +
              (node.position.y - newNodePosition.y) ** 2
          ),
        }))
        .filter((node) => node.distance <= MAX_AUTO_CONNECT_DISTANCE) // Only consider nodes within reasonable distance
        .sort((a, b) => a.distance - b.distance)

      return existingNodes[0] || null
    },
    [nodes]
  )

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault()

      const data = event.dataTransfer.getData("application/reactflow")

      if (typeof data === "undefined" || !data) {
        return
      }

      const blockData = JSON.parse(data)
      const position = screenToFlowPosition({
        x: event.clientX,
        y: event.clientY,
      })
      console.log('🎯 Drop position (working):', position);

      const newNode: Node = {
        id: `${blockData.type}-${Date.now()}`,
        type: "custom",
        position,
        data: {
          label: blockData.name,
          type: blockData.type,
          description: blockData.description,
        },
      }

      // Add the node first
      addNode(newNode)

      // Auto-connect to the nearest node if one exists
      if (nodes.length > 0) {
        const closestNode = findClosestNode(position)
        if (closestNode) {
          console.log(`🔗 Auto-connecting new node "${newNode.data.label}" to closest node "${closestNode.data.label}"`)

          // Create an edge from the closest node's exit point to the new node's entry point
          const autoConnectEdge = {
            id: `edge-${closestNode.id}-${newNode.id}`,
            source: closestNode.id,
            target: newNode.id,
            type: "shoshin",
            animated: true,
          }

          // Connect the nodes using the store's connectNodes method
          connectNodes(autoConnectEdge)
        } else {
          console.log(`ℹ️ No suitable node found for auto-connection (all nodes too far from new position)`)
        }
      } else {
        console.log(`ℹ️ No existing nodes to connect to`)
      }
    },
    [screenToFlowPosition, addNode, connectNodes, nodes, findClosestNode]
  )

  return (
    <div className="w-full h-full" ref={reactFlowWrapper}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onInit={setReactFlowInstance}
        onDrop={onDrop}
        onDragOver={onDragOver}
        onSelectionChange={onSelectionChange}
        onMouseMove={onMouseMove}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        fitView
        className="bg-background"
        defaultEdgeOptions={{
          type: 'shoshin',
          animated: true,
          markerEnd: {
            type: MarkerType.ArrowClosed,
            color: '#9ca3af',
          },
        }}
        connectionLineStyle={{
          stroke: '#9ca3af',
          strokeWidth: 2,
          strokeDasharray: '5,5'
        }}
        snapToGrid={true}
        snapGrid={[15, 15]}
      >
        {editorSettings.showCanvasDots && (
          <Background
            color="currentColor"
            gap={20}
            size={1}
          />
        )}
        <Controls
          position="top-left"
          className="bg-background border-border [&>button]:bg-neutral-100 dark:[&>button]:bg-neutral-800 [&>button]:border-border [&>button]:text-foreground [&>button:hover]:bg-neutral-200 dark:[&>button:hover]:bg-neutral-700"
          style={{
            left: isCollapsed ? '96px' : '360px', // Offset by outermost sidebar (80px) + secondary sidebar (264px when expanded) + padding (16px)
            top: '16px'   // Add some top margin
          }}
        />
        {editorSettings.showMinimap && (
          <MiniMap
            className="
              bg-white/3 dark:bg-black/5
              border border-white/8 dark:border-white/3
            "
            style={{
              width: 160,
              height: 100,
              borderRadius: '12px',
            }}
            nodeColor={(node) => {
              // Elegant, minimalistic node colors with better transparency
              const nodeType = node.data?.type;
              switch (nodeType) {
                case 'start':
                  return 'rgba(34, 197, 94, 0.8)'; // Emerald
                case 'agent':
                  return 'rgba(168, 85, 247, 0.8)'; // Purple
                case 'knowledge':
                  return 'rgba(59, 130, 246, 0.8)'; // Blue
                case 'response':
                  return 'rgba(239, 68, 68, 0.8)'; // Rose
                case 'condition':
                  return 'rgba(245, 158, 11, 0.8)'; // Amber
                case 'function':
                  return 'rgba(16, 185, 129, 0.8)'; // Teal
                default:
                  return 'rgba(107, 114, 128, 0.7)'; // Neutral
              }
            }}
            nodeStrokeColor={(node) => {
              // Subtle stroke for definition
              const nodeType = node.data?.type;
              switch (nodeType) {
                case 'start':
                  return 'rgba(34, 197, 94, 0.9)';
                case 'agent':
                  return 'rgba(168, 85, 247, 0.9)';
                case 'knowledge':
                  return 'rgba(59, 130, 246, 0.9)';
                case 'response':
                  return 'rgba(239, 68, 68, 0.9)';
                case 'condition':
                  return 'rgba(245, 158, 11, 0.9)';
                case 'function':
                  return 'rgba(16, 185, 129, 0.9)';
                // Tool nodes
                case 'vision':
                  return 'rgba(77, 95, 255, 0.9)';
                case 'translate':
                  return 'rgba(255, 75, 75, 0.9)';
                case 'tavily':
                  return 'rgba(0, 102, 255, 0.9)';
                case 's3':
                  return 'rgba(224, 224, 224, 0.9)';
                case 'reddit':
                  return 'rgba(255, 87, 0, 0.9)';
                case 'evaluator':
                  return 'rgba(77, 95, 255, 0.9)';
                case 'file':
                  return 'rgba(64, 145, 108, 0.9)';
                case 'gmail':
                  return 'rgba(224, 224, 224, 0.9)';
                case 'google_sheets':
                  return 'rgba(224, 224, 224, 0.9)';
                case 'google_drive':
                  return 'rgba(224, 224, 224, 0.9)';
                default:
                  return 'rgba(107, 114, 128, 0.8)';
              }
            }}
            nodeStrokeWidth={1}
            maskColor="rgba(0, 0, 0, 0.02)"
            pannable
            zoomable
            position="bottom-right"
            ariaLabel="Canvas minimap overview"
          />
        )}
      </ReactFlow>
      <ToastContainer toasts={toasts} onRemove={removeToast} />

      {/* Node Settings Modal */}
      <NodeSettingsModal
        open={nodeSettingsModal.open}
        onOpenChange={(open) => setNodeSettingsModal(open, nodeSettingsModal.nodeId)}
        nodeId={nodeSettingsModal.nodeId}
      />
    </div>
  )
}

export function MainCanvas() {
  return (
    <ReactFlowProvider>
      <MainCanvasInner />
    </ReactFlowProvider>
  )
}
