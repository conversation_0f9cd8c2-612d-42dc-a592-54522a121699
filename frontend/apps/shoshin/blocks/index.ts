import {
  getAllBlocks,
  getAllBlockTypes,
  getBlock,
  getBlocksByCategory,
  isValidBlockType,
  registry,
} from "./registry";

import {
  coreBlocks,
  getAllCoreBlocks,
  getCoreBlock,
  getCoreBlocksByCategory,
} from "./core-blocks";

export {
  coreBlocks,
  getAllBlocks,
  getAllBlockTypes,
  getAllCoreBlocks,
  getBlock,
  getBlocksByCategory,
  getCoreBlock,
  getCoreBlocksByCategory,
  isValidBlockType,
  registry,
};

export type { CoreBlockConfig } from "./core-blocks";
export type { BlockConfig } from "./types";
