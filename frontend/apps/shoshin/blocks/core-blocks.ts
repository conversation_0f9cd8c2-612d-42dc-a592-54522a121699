/**
 * Core Block Nodes for Shoshin Editor
 * These are the fundamental workflow blocks like start, response, knowledge, etc.
 */

import {
  Brain,
  Code,
  Database,
  GitBranch,
  Globe,
  MessageSquare,
  Play,
  RotateCcw,
  Route,
  User,
  Workflow
} from "lucide-react"

export interface CoreBlockConfig {
  id: string
  name: string
  description: string
  icon: any
  color: string
  category: 'blocks'
}

// Core workflow blocks
export const coreBlocks: CoreBlockConfig[] = [
  {
    id: "start",
    name: "Start",
    description: "Start workflow",
    icon: Play,
    color: "#6B7280",
    category: 'blocks'
  },
  {
    id: "agent",
    name: "Agent",
    description: "Build an agent",
    icon: User,
    color: "#802FFF",
    category: 'blocks'
  },
  {
    id: "api",
    name: "API",
    description: "Use any API",
    icon: Globe,
    color: "#2F55FF",
    category: 'blocks'
  },
  {
    id: "condition",
    name: "Condition",
    description: "Add a condition",
    icon: GitBranch,
    color: "#FF752F",
    category: 'blocks'
  },
  {
    id: "function",
    name: "Function",
    description: "Run custom logic",
    icon: Code,
    color: "#FF402F",
    category: 'blocks'
  },
  {
    id: "router",
    name: "Router",
    description: "Route workflow",
    icon: Route,
    color: "#28C43F",
    category: 'blocks'
  },
  {
    id: "memory",
    name: "Memory",
    description: "Add memory store",
    icon: Database,
    color: "#F64F9E",
    category: 'blocks'
  },
  {
    id: "knowledge",
    name: "Knowledge",
    description: "Use vector search",
    icon: Brain,
    color: "#1F40ED",
    category: 'blocks'
  },
  {
    id: "workflow",
    name: "Workflow",
    description: "Execute another workflow",
    icon: Workflow,
    color: "#FFC83C",
    category: 'blocks'
  },
  {
    id: "response",
    name: "Response",
    description: "Send structured API response",
    icon: MessageSquare,
    color: "#4D5FFF",
    category: 'blocks'
  },
  {
    id: "loop",
    name: "Loop",
    description: "Iterate over items",
    icon: RotateCcw,
    color: "#2FB3FF",
    category: 'blocks'
  }
]

// Helper functions
export const getCoreBlock = (id: string): CoreBlockConfig | undefined => 
  coreBlocks.find(block => block.id === id)

export const getAllCoreBlocks = (): CoreBlockConfig[] => coreBlocks

export const getCoreBlocksByCategory = (category: 'blocks'): CoreBlockConfig[] =>
  coreBlocks.filter(block => block.category === category)
