import { FileText } from 'lucide-react'
import type { BlockConfig, SubBlockConfig, SubBlockLayout, SubBlockType } from '../types'

interface FileParserOutput {
  output: {
    content: string
    metadata: any
    files: any[]
  }
}

const shouldEnableURLInput = process.env.NODE_ENV === 'production'

const inputMethodBlock: SubBlockConfig = {
  id: 'inputMethod',
  title: 'Select Input Method',
  type: 'dropdown' as SubBlockType,
  layout: 'full' as SubBlockLayout,
  options: [
    { id: 'url', label: 'File URL' },
    { id: 'upload', label: 'Upload Files' },
  ],
}

const fileUploadBlock: SubBlockConfig = {
  id: 'file',
  title: 'Upload Files',
  type: 'file-upload' as SubBlockType,
  layout: 'full' as SubBlockLayout,
  acceptedTypes: '.pdf,.csv,.docx',
  multiple: true,
  maxSize: 100, // 100MB max via direct upload
}

export const FileBlock: BlockConfig<FileParserOutput> = {
  type: 'file',
  name: 'File Upload',
  description: 'Read and parse multiple files',
  longDescription: `Upload and extract contents from structured file formats including PDFs, CSV spreadsheets, and Word documents (DOCX). ${
    shouldEnableURLInput
      ? 'You can either provide a URL to a file or upload files directly. '
      : 'Upload files directly. '
  }Specialized parsers extract text and metadata from each format. You can upload multiple files at once and access them individually or as a combined document.`,
  docsLink: 'https://docs.simstudio.ai/tools/file',
  category: 'tools',
  bgColor: '#40916C',
  icon: FileText,
  subBlocks: [
    ...(shouldEnableURLInput ? [inputMethodBlock] : []),
    {
      id: 'filePath',
      title: 'File URL',
      type: 'short-input' as SubBlockType,
      layout: 'full' as SubBlockLayout,
      placeholder: 'Enter file URL',
      condition: shouldEnableURLInput
        ? { field: 'inputMethod', value: 'url' }
        : undefined,
    },
    {
      ...fileUploadBlock,
      condition: shouldEnableURLInput
        ? { field: 'inputMethod', value: 'upload' }
        : undefined,
    },
    {
      id: 'outputFormat',
      title: 'Output Format',
      type: 'dropdown' as SubBlockType,
      layout: 'full' as SubBlockLayout,
      options: [
        { id: 'combined', label: 'Combined Text' },
        { id: 'individual', label: 'Individual Files' },
        { id: 'structured', label: 'Structured Data' },
      ],
    },
    {
      id: 'includeMetadata',
      title: 'Include Metadata',
      type: 'switch' as SubBlockType,
      layout: 'full' as SubBlockLayout,
    },
  ],
  tools: {
    access: ['file_parser'],
    config: {
      tool: () => 'file_parser',
      params: (params: Record<string, any>) => {
        const { file, filePath, inputMethod, ...rest } = params
        
        // Determine the file source
        const fileSource = shouldEnableURLInput && inputMethod === 'url' ? filePath : file
        
        return {
          ...rest,
          file: fileSource,
        }
      },
    },
  },
  inputs: {
    ...(shouldEnableURLInput ? { inputMethod: { type: 'string', required: false } } : {}),
    filePath: { type: 'string', required: false },
    file: { type: 'string', required: false },
    outputFormat: { type: 'string', required: false },
    includeMetadata: { type: 'boolean', required: false },
  },
  outputs: {
    content: 'string',
    metadata: 'json',
    files: 'json',
  },
}
