import { MessageCircle } from 'lucide-react'
import type { BlockConfig } from '../types'

interface RedditResponse {
  output: {
    subreddit: string
    posts: any[]
    post: any
    comments: any[]
  }
}

export const RedditBlock: BlockConfig<RedditResponse> = {
  type: 'reddit',
  name: 'Reddit',
  description: 'Access Reddit data and content',
  longDescription:
    'Access Reddit data to retrieve posts and comments from any subreddit. Get post titles, content, authors, scores, comments and more.',
  docsLink: 'https://docs.simstudio.ai/tools/reddit',
  category: 'tools',
  bgColor: '#FF5700',
  icon: MessageCircle,
  subBlocks: [
    // Action selection
    {
      id: 'action',
      title: 'Action',
      type: 'dropdown',
      layout: 'full',
      options: [
        { label: 'Get Posts', id: 'get_posts' },
        { label: 'Get Comments', id: 'get_comments' },
      ],
    },

    // Common fields - appear for all actions
    {
      id: 'subreddit',
      title: 'Subreddit',
      type: 'short-input',
      layout: 'full',
      placeholder: 'Enter subreddit name (without r/)',
      condition: {
        field: 'action',
        value: ['get_posts', 'get_comments'],
      },
    },

    // Get Posts specific fields
    {
      id: 'sort',
      title: 'Sort By',
      type: 'dropdown',
      layout: 'full',
      options: [
        { label: 'Hot', id: 'hot' },
        { label: 'New', id: 'new' },
        { label: 'Top', id: 'top' },
        { label: 'Rising', id: 'rising' },
      ],
      condition: {
        field: 'action',
        value: 'get_posts',
      },
    },
    {
      id: 'time',
      title: 'Time Filter (for Top sort)',
      type: 'dropdown',
      layout: 'full',
      options: [
        { label: 'Day', id: 'day' },
        { label: 'Week', id: 'week' },
        { label: 'Month', id: 'month' },
        { label: 'Year', id: 'year' },
        { label: 'All Time', id: 'all' },
      ],
      condition: {
        field: 'action',
        value: 'get_posts',
        and: {
          field: 'sort',
          value: 'top',
        },
      },
    },
    {
      id: 'limit',
      title: 'Max Posts',
      type: 'short-input',
      layout: 'full',
      placeholder: '10',
      condition: {
        field: 'action',
        value: 'get_posts',
      },
    },

    // Get Comments specific fields
    {
      id: 'postId',
      title: 'Post ID',
      type: 'short-input',
      layout: 'full',
      placeholder: 'Enter post ID',
      condition: {
        field: 'action',
        value: 'get_comments',
      },
    },
    {
      id: 'commentSort',
      title: 'Sort Comments By',
      type: 'dropdown',
      layout: 'full',
      options: [
        { label: 'Confidence', id: 'confidence' },
        { label: 'Top', id: 'top' },
        { label: 'New', id: 'new' },
        { label: 'Controversial', id: 'controversial' },
        { label: 'Old', id: 'old' },
        { label: 'Random', id: 'random' },
        { label: 'Q&A', id: 'qa' },
      ],
      condition: {
        field: 'action',
        value: 'get_comments',
      },
    },
    {
      id: 'commentLimit',
      title: 'Number of Comments',
      type: 'short-input',
      layout: 'full',
      placeholder: '50',
      condition: {
        field: 'action',
        value: 'get_comments',
      },
    },
  ],
  tools: {
    access: ['reddit_hot_posts', 'reddit_get_posts', 'reddit_get_comments'],
    config: {
      tool: (inputs) => {
        const action = inputs.action || 'get_posts'

        if (action === 'get_comments') {
          return 'reddit_get_comments'
        }

        return 'reddit_get_posts'
      },
      params: (inputs) => {
        const action = inputs.action || 'get_posts'

        if (action === 'get_comments') {
          return {
            postId: inputs.postId,
            subreddit: inputs.subreddit,
            sort: inputs.commentSort,
            limit: inputs.commentLimit ? Number.parseInt(inputs.commentLimit) : undefined,
          }
        }

        return {
          subreddit: inputs.subreddit,
          sort: inputs.sort,
          limit: inputs.limit ? Number.parseInt(inputs.limit) : undefined,
          time: inputs.sort === 'top' ? inputs.time : undefined,
        }
      },
    },
  },
  inputs: {
    action: { type: 'string', required: true },
    subreddit: { type: 'string', required: true },
    sort: { type: 'string', required: true },
    time: { type: 'string', required: false },
    limit: { type: 'number', required: false },
    postId: { type: 'string', required: true },
    commentSort: { type: 'string', required: false },
    commentLimit: { type: 'number', required: false },
  },
  outputs: {
    subreddit: 'string',
    posts: 'json',
    post: 'json',
    comments: 'json',
  },
}
