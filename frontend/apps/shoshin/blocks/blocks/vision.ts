import { Eye } from 'lucide-react'
import type { BlockConfig } from '../types'

interface VisionResponse {
  output: {
    content: string
    model: string
    tokens: any
  }
}

export const VisionBlock: BlockConfig<VisionResponse> = {
  type: 'vision',
  name: 'Analyze Image',
  description: 'Analyze images with vision models',
  longDescription:
    'Process visual content with customizable prompts to extract insights and information from images.',
  docsLink: 'https://docs.simstudio.ai/tools/vision',
  category: 'tools',
  bgColor: '#4D5FFF',
  icon: Eye,
  subBlocks: [
    {
      id: 'imageUrl',
      title: 'Image URL',
      type: 'short-input',
      layout: 'full',
      placeholder: 'Enter publicly accessible image URL',
    },
    {
      id: 'model',
      title: 'Vision Model',
      type: 'dropdown',
      layout: 'half',
      options: ['gpt-4o', 'claude-3-opus-20240229', 'claude-3-sonnet-20240229'],
    },
    {
      id: 'prompt',
      title: 'Prompt',
      type: 'long-input',
      layout: 'full',
      placeholder: 'Enter prompt for image analysis',
    },
    {
      id: 'apiKey',
      title: 'API Key',
      type: 'short-input',
      layout: 'full',
      placeholder: 'Enter your API key',
      password: true,
    },
  ],
  tools: {
    access: ['vision_tool'],
  },
  inputs: {
    apiKey: { type: 'string', required: true },
    imageUrl: { type: 'string', required: true },
    model: { type: 'string', required: false },
    prompt: { type: 'string', required: false },
  },
  outputs: {
    content: 'string',
    model: 'any',
    tokens: 'any',
  },
}
