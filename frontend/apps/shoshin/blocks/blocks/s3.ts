import { Database } from 'lucide-react'
import type { BlockConfig } from '../types'

interface S3Response {
  output: {
    url: string
    key: string
    bucket: string
  }
}

export const S3Block: BlockConfig<S3Response> = {
  type: 's3',
  name: 'S3',
  description: 'View S3 files',
  longDescription: 'Retrieve and view files from Amazon S3 buckets using presigned URLs.',
  docsLink: 'https://docs.simstudio.ai/tools/s3',
  category: 'tools',
  bgColor: '#E0E0E0',
  icon: Database,
  subBlocks: [
    {
      id: 'accessKeyId',
      title: 'Access Key ID',
      type: 'short-input',
      layout: 'full',
      placeholder: 'Enter your AWS Access Key ID',
      password: true,
    },
    {
      id: 'secretAccessKey',
      title: 'Secret Access Key',
      type: 'short-input',
      layout: 'full',
      placeholder: 'Enter your AWS Secret Access Key',
      password: true,
    },
    {
      id: 'region',
      title: 'Region',
      type: 'short-input',
      layout: 'full',
      placeholder: 'Enter AWS region (e.g., us-east-1)',
    },
    {
      id: 'bucket',
      title: 'Bucket Name',
      type: 'short-input',
      layout: 'full',
      placeholder: 'Enter S3 bucket name',
    },
    {
      id: 'key',
      title: 'Object Key',
      type: 'short-input',
      layout: 'full',
      placeholder: 'Enter S3 object key (file path)',
    },
    {
      id: 'operation',
      title: 'Operation',
      type: 'dropdown',
      layout: 'full',
      options: [
        { label: 'Get Presigned URL', id: 'get_presigned_url' },
        { label: 'List Objects', id: 'list_objects' },
        { label: 'Upload File', id: 'upload_file' },
      ],
    },
    {
      id: 'expiresIn',
      title: 'URL Expires In (seconds)',
      type: 'short-input',
      layout: 'full',
      placeholder: '3600',
      condition: { field: 'operation', value: 'get_presigned_url' },
    },
  ],
  tools: {
    access: ['s3_get_presigned_url', 's3_list_objects', 's3_upload_file'],
    config: {
      tool: (params: Record<string, any>) => {
        return params.operation || 's3_get_presigned_url'
      },
    },
  },
  inputs: {
    accessKeyId: { type: 'string', required: true },
    secretAccessKey: { type: 'string', required: true },
    region: { type: 'string', required: true },
    bucket: { type: 'string', required: true },
    key: { type: 'string', required: false },
    operation: { type: 'string', required: true },
    expiresIn: { type: 'number', required: false },
  },
  outputs: {
    url: 'string',
    key: 'string',
    bucket: 'string',
  },
}
