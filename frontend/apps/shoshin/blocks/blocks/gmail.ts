import { Mail } from 'lucide-react'
import type { BlockConfig } from '../types'

interface GmailToolResponse {
  output: {
    content: string
    metadata: any
  }
}

export const GmailBlock: BlockConfig<GmailToolResponse> = {
  type: 'gmail',
  name: 'Gmail',
  description: 'Send Gmail',
  longDescription:
    'Integrate Gmail functionality to send email messages within your workflow. Automate email communications and process email content using OAuth authentication.',
  docsLink: 'https://docs.simstudio.ai/tools/gmail',
  category: 'tools',
  bgColor: '#E0E0E0',
  icon: Mail,
  subBlocks: [
    // Gmail Credentials
    {
      id: 'credential',
      title: 'Gmail Account',
      type: 'oauth-input',
      layout: 'full',
      provider: 'google-email',
      serviceId: 'gmail',
      requiredScopes: [
        'https://www.googleapis.com/auth/gmail.send',
        'https://www.googleapis.com/auth/gmail.modify',
        'https://www.googleapis.com/auth/gmail.labels',
      ],
      placeholder: 'Select Gmail account',
    },
    // Send Email Fields
    {
      id: 'to',
      title: 'To',
      type: 'short-input',
      layout: 'full',
      placeholder: 'Recipient email address',
    },
    {
      id: 'subject',
      title: 'Subject',
      type: 'short-input',
      layout: 'full',
      placeholder: 'Email subject',
    },
    {
      id: 'body',
      title: 'Body',
      type: 'long-input',
      layout: 'full',
      placeholder: 'Email content',
    },
    {
      id: 'cc',
      title: 'CC',
      type: 'short-input',
      layout: 'full',
      placeholder: 'CC email addresses (comma separated)',
    },
    {
      id: 'bcc',
      title: 'BCC',
      type: 'short-input',
      layout: 'full',
      placeholder: 'BCC email addresses (comma separated)',
    },
    {
      id: 'includeHtml',
      title: 'Include HTML',
      type: 'switch',
      layout: 'full',
    },
    {
      id: 'htmlMessage',
      title: 'HTML Body',
      type: 'long-input',
      layout: 'full',
      placeholder: 'HTML email content',
      condition: { field: 'includeHtml', value: 'true' },
    },
  ],
  tools: {
    access: ['gmail_send', 'gmail_read', 'gmail_search'],
    config: {
      tool: (params) => {
        // Since we only have send_gmail now, we can simplify this
        return 'gmail_send'
      },
      params: (params) => {
        // Pass the credential directly from the credential field
        const { credential, ...rest } = params

        return {
          ...rest,
          credential, // Keep the credential parameter
        }
      },
    },
  },
  inputs: {
    credential: { type: 'string', required: true },
    // Send operation inputs
    to: { type: 'string', required: false },
    subject: { type: 'string', required: false },
    body: { type: 'string', required: false },
    cc: { type: 'string', required: false },
    bcc: { type: 'string', required: false },
    includeHtml: { type: 'boolean', required: false },
    htmlMessage: { type: 'string', required: false },
  },
  outputs: {
    content: 'string',
    metadata: 'json',
  },
}
