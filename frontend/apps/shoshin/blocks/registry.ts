/**
 * Blocks Registry for Shoshin
 */

// Import all blocks
import { VisionBlock } from './blocks/vision'
import { TranslateBlock } from './blocks/translate'
import { TavilyBlock } from './blocks/tavily'
import { S3Block } from './blocks/s3'
import { RedditBlock } from './blocks/reddit'
import { EvaluatorBlock } from './blocks/evaluator'
import { FileBlock } from './blocks/file'
import { GmailBlock } from './blocks/gmail'
import { GoogleSheetsBlock } from './blocks/google_sheets'
import { GoogleDriveBlock } from './blocks/google_drive'

import type { BlockConfig } from './types'

// Registry of all available blocks
export const registry: Record<string, BlockConfig> = {
  vision: VisionBlock,
  translate: TranslateBlock,
  tavily: TavilyBlock,
  s3: S3Block,
  reddit: RedditBlock,
  evaluator: EvaluatorBlock,
  file: FileBlock,
  gmail: GmailBlock,
  google_sheets: GoogleSheetsBlock,
  google_drive: GoogleDriveBlock,
}

// Helper functions to access the registry
export const getBlock = (type: string): BlockConfig | undefined => registry[type]

export const getBlocksByCategory = (category: 'blocks' | 'tools'): BlockConfig[] =>
  Object.values(registry).filter((block) => block.category === category)

export const getAllBlockTypes = (): string[] => Object.keys(registry)

export const isValidBlockType = (type: string): type is string => type in registry

export const getAllBlocks = (): BlockConfig[] => Object.values(registry)

// Export specific blocks for easier access
export {
  VisionBlock,
  TranslateBlock,
  TavilyBlock,
  S3Block,
  RedditBlock,
  EvaluatorBlock,
  FileBlock,
  GmailBlock,
  GoogleSheetsBlock,
  GoogleDriveBlock,
}
