import type { LucideIcon } from 'lucide-react'

// Block categories
export type BlockCategory = 'blocks' | 'tools'

// Sub-block types for UI components
export type SubBlockType = 
  | 'short-input'
  | 'long-input' 
  | 'dropdown'
  | 'switch'
  | 'code'
  | 'file-upload'
  | 'oauth-input'
  | 'file-selector'
  | 'folder-selector'
  | 'eval-input'
  | 'response-format'
  | 'table'

// Layout options for sub-blocks
export type SubBlockLayout = 'full' | 'half'

// Parameter types
export type ParamType = 'string' | 'number' | 'boolean' | 'json' | 'any'

// Block icon type
export type BlockIcon = LucideIcon | React.ComponentType<any>

// Condition for conditional sub-blocks
export interface SubBlockCondition {
  field: string
  value: string | string[]
  not?: boolean
  and?: SubBlockCondition
}

// Sub-block configuration
export interface SubBlockConfig {
  id: string
  title: string
  type: SubBlockType
  layout: SubBlockLayout
  placeholder?: string
  description?: string
  options?: string[] | Array<{ id: string; label: string }>
  condition?: SubBlockCondition
  hidden?: boolean
  password?: boolean
  connectionDroppable?: boolean
  value?: (params: Record<string, any>) => string
  acceptedTypes?: string
  multiple?: boolean
  maxSize?: number
  provider?: string
  serviceId?: string
  requiredScopes?: string[]
  mimeType?: string
  language?: string
  generationType?: string
  columns?: string[]
}

// Parameter configuration
export interface ParamConfig {
  type: ParamType
  required: boolean
  description?: string
  schema?: any
}

// Tool response interface
export interface ToolResponse {
  output: any
}

// Output value types
export type OutputValueType = 'string' | 'number' | 'boolean' | 'json' | 'any'

// Tool output to value type mapping
export type ToolOutputToValueType<T> = {
  [K in keyof T]: OutputValueType
}

// Extract tool output type
export type ExtractToolOutput<T extends ToolResponse> = T['output']

// Block output types
export type BlockOutput = 'string' | 'number' | 'boolean' | 'json' | 'any'

// Main block configuration interface
export interface BlockConfig<T extends ToolResponse = ToolResponse> {
  type: string
  name: string
  description: string
  category: BlockCategory
  longDescription?: string
  docsLink?: string
  bgColor: string
  icon: BlockIcon
  subBlocks: SubBlockConfig[]
  tools: {
    access: string[]
    config?: {
      tool: (params: Record<string, any>) => string
      params?: (params: Record<string, any>) => Record<string, any>
    }
  }
  inputs: Record<string, ParamConfig>
  outputs: ToolOutputToValueType<ExtractToolOutput<T>> & {
    visualization?: {
      type: 'image'
      url: string
    }
  }
  hideFromToolbar?: boolean
}

// Output configuration rules
export interface OutputConfig {
  type: BlockOutput
}
