"""
Tests for health check endpoints.
"""

import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)


def test_root_endpoint():
    """Test the root endpoint."""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "Shoshin AI" in data["message"]
    assert data["version"] == "1.0.0"


def test_health_check_endpoint():
    """Test the comprehensive health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200

    data = response.json()

    # Check required fields
    assert "status" in data
    assert "timestamp" in data
    assert "service" in data
    assert "version" in data

    # Check service info
    assert data["service"] == "shoshin-ai-backend"
    assert data["version"] == "1.0.0"

    # Status should be one of the valid values
    assert data["status"] in ["healthy", "degraded", "unhealthy"]


def test_simple_health_check_endpoint():
    """Test the simple health check endpoint."""
    response = client.get("/health/simple")
    assert response.status_code == 200

    data = response.json()

    # Check required fields
    assert "status" in data
    assert "timestamp" in data
    assert "service" in data

    # Check values
    assert data["status"] == "healthy"
    assert data["service"] == "shoshin-ai-backend"


def test_health_check_response_time():
    """Test that health check responds quickly."""
    import time

    start_time = time.time()
    response = client.get("/health/simple")
    end_time = time.time()

    response_time = end_time - start_time

    # Health check should respond within 5 seconds
    assert response_time < 5.0
    assert response.status_code == 200
