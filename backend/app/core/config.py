"""
Configuration settings for Shoshin AI backend.
"""

from pydantic_settings import BaseSettings
from typing import List, Optional
from functools import lru_cache
import os


class Settings(BaseSettings):
    """Application settings."""
    
    # Application
    APP_NAME: str = "Shoshin AI"
    DEBUG: bool = False
    VERSION: str = "1.0.0"
    ENVIRONMENT: str = "development"
    
    # Server
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # Security
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Google OAuth
    GOOGLE_CLIENT_ID: Optional[str] = None
    GOOGLE_CLIENT_SECRET: Optional[str] = None
    GOOGLE_REDIRECT_URI: str = "http://localhost:3000/auth/callback"
    
    # OpenAI
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_MODEL: str = "gpt-4"
    
    # Google Gemini
    GOOGLE_API_KEY: Optional[str] = None
    GEMINI_MODEL: str = "gemini-pro-vision"
    
    # FAL.AI
    FAL_API_KEY: Optional[str] = None
    
    # Langfuse
    LANGFUSE_PUBLIC_KEY: Optional[str] = None
    LANGFUSE_SECRET_KEY: Optional[str] = None
    LANGFUSE_HOST: str = "https://cloud.langfuse.com"
    
    # Redis (for background tasks)
    REDIS_URL: str = "redis://localhost:6379"
    
    # File storage
    UPLOAD_DIR: str = "uploads"
    GENERATED_ADS_DIR: str = "generated_ads"
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_IMAGE_TYPES: List[str] = ["image/jpeg", "image/png", "image/webp"]
    
    # Image generation settings
    MAX_GENERATED_IMAGES: int = 10
    IMAGE_GENERATION_MODELS: List[str] = [
        "fal-ai/flux/schnell",
        "fal-ai/flux/dev",
        "fal-ai/stable-diffusion-v3-medium",
        "fal-ai/aura-flow"
    ]
    
    # Database (optional)
    DATABASE_URL: Optional[str] = None
    
    # Health check settings
    HEALTH_CHECK_TIMEOUT: int = 30
    MEMORY_THRESHOLD_WARNING: int = 80
    MEMORY_THRESHOLD_CRITICAL: int = 90
    DISK_THRESHOLD_WARNING: int = 85
    DISK_THRESHOLD_CRITICAL: int = 95
    CPU_THRESHOLD_WARNING: int = 85
    CPU_THRESHOLD_CRITICAL: int = 95
    
    class Config:
        env_file = ".env"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()
