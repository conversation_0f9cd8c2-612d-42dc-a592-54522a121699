"""
Shoshin AI - FastAPI Backend
Main application entry point for the Shoshin AI advertisement generation platform.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import os
from contextlib import asynccontextmanager

from app.core.config import get_settings
from app.api import health


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events."""
    # Startup
    print("🚀 Starting Shoshin AI Backend...")

    # Create upload directories if they don't exist
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("generated_ads", exist_ok=True)

    yield

    # Shutdown
    print("🛑 Shutting down Shoshin AI Backend...")


# Create FastAPI application
app = FastAPI(
    title="Shoshin AI API",
    description="AI-powered product advertisement generation platform inspired by N8N workflow design",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Get settings
settings = get_settings()

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health.router, tags=["health"])


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
