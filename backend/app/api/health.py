"""
Health check routes for Shoshin AI.
"""

from fastapi import APIRouter
from fastapi.responses import JSONResponse
import os
import psutil
import time
import platform
from datetime import datetime

from app.core.config import get_settings

router = APIRouter()

# Global variable to track application start time
app_start_time = time.time()


@router.get("/")
async def root():
    """Root endpoint with basic information."""
    return {
        "message": "Welcome to Shoshin AI - Your Personal Professional Photographer for Products",
        "description": "AI-powered product advertisement generation platform",
        "version": "1.0.0",
        "docs": "/docs",
        "health_check": "/health"
    }


@router.get("/health")
async def health_check():
    """
    Comprehensive health check endpoint.
    
    Returns detailed information about the application status including:
    - Application status
    - System resources
    - Uptime
    - Dependencies status
    """
    try:
        settings = get_settings()
        
        # Calculate uptime
        current_time = time.time()
        uptime_seconds = current_time - app_start_time
        uptime_formatted = format_uptime(uptime_seconds)
        
        # Get system information
        memory_info = psutil.virtual_memory()
        disk_info = psutil.disk_usage('/')
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Check directory existence
        directories_status = {
            "uploads": os.path.exists("uploads"),
            "generated_ads": os.path.exists("generated_ads")
        }
        
        # Basic dependency checks
        dependencies_status = check_dependencies()
        
        health_data = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "service": "shoshin-ai-backend",
            "version": "1.0.0",
            "uptime": {
                "seconds": int(uptime_seconds),
                "formatted": uptime_formatted
            },
            "system": {
                "cpu_usage_percent": cpu_percent,
                "memory": {
                    "total_gb": round(memory_info.total / (1024**3), 2),
                    "available_gb": round(memory_info.available / (1024**3), 2),
                    "used_percent": memory_info.percent
                },
                "disk": {
                    "total_gb": round(disk_info.total / (1024**3), 2),
                    "free_gb": round(disk_info.free / (1024**3), 2),
                    "used_percent": round((disk_info.used / disk_info.total) * 100, 2)
                }
            },
            "directories": directories_status,
            "dependencies": dependencies_status,
            "environment": {
                "python_version": f"{platform.python_version()}",
                "platform": platform.system(),
                "debug_mode": settings.DEBUG
            }
        }
        
        # Determine overall health status
        overall_status = determine_health_status(health_data)
        health_data["status"] = overall_status
        
        # Return appropriate HTTP status code
        if overall_status == "healthy":
            return JSONResponse(content=health_data, status_code=200)
        elif overall_status == "degraded":
            return JSONResponse(content=health_data, status_code=200)
        else:
            return JSONResponse(content=health_data, status_code=503)
            
    except Exception as e:
        # If health check itself fails
        error_response = {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "service": "shoshin-ai-backend",
            "error": str(e),
            "message": "Health check failed"
        }
        return JSONResponse(content=error_response, status_code=503)


@router.get("/health/simple")
async def simple_health_check():
    """
    Simple health check endpoint for basic monitoring.
    Returns minimal response for quick status checks.
    """
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "shoshin-ai-backend"
    }


def format_uptime(seconds: float) -> str:
    """Format uptime in human-readable format."""
    days = int(seconds // 86400)
    hours = int((seconds % 86400) // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    
    if days > 0:
        return f"{days}d {hours}h {minutes}m {secs}s"
    elif hours > 0:
        return f"{hours}h {minutes}m {secs}s"
    elif minutes > 0:
        return f"{minutes}m {secs}s"
    else:
        return f"{secs}s"


def check_dependencies() -> dict:
    """Check status of key dependencies."""
    dependencies = {}
    
    # Check if required packages are importable
    try:
        import fastapi
        dependencies["fastapi"] = {"status": "available", "version": fastapi.__version__}
    except ImportError:
        dependencies["fastapi"] = {"status": "unavailable", "error": "Import failed"}
    
    try:
        import uvicorn
        dependencies["uvicorn"] = {"status": "available", "version": uvicorn.__version__}
    except ImportError:
        dependencies["uvicorn"] = {"status": "unavailable", "error": "Import failed"}
    
    try:
        import pydantic
        dependencies["pydantic"] = {"status": "available", "version": pydantic.__version__}
    except ImportError:
        dependencies["pydantic"] = {"status": "unavailable", "error": "Import failed"}
    
    return dependencies


def determine_health_status(health_data: dict) -> str:
    """Determine overall health status based on various metrics."""
    # Check critical thresholds
    memory_usage = health_data["system"]["memory"]["used_percent"]
    disk_usage = health_data["system"]["disk"]["used_percent"]
    cpu_usage = health_data["system"]["cpu_usage_percent"]
    
    # Check if directories exist
    directories_ok = all(health_data["directories"].values())
    
    # Check dependencies
    dependencies_ok = all(
        dep.get("status") == "available" 
        for dep in health_data["dependencies"].values()
    )
    
    # Determine status
    if not directories_ok or not dependencies_ok:
        return "unhealthy"
    elif memory_usage > 90 or disk_usage > 95 or cpu_usage > 95:
        return "degraded"
    elif memory_usage > 80 or disk_usage > 85 or cpu_usage > 85:
        return "degraded"
    else:
        return "healthy"
