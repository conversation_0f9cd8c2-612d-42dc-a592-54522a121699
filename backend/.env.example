# Shoshin AI Backend Environment Variables

# Application Settings
APP_NAME=Shoshin AI
DEBUG=false
ENVIRONMENT=development

# Server Settings
HOST=0.0.0.0
PORT=8000

# Security
SECRET_KEY=your-secret-key-change-in-production

# Google OAuth (optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# OpenAI (optional)
OPENAI_API_KEY=your-openai-api-key

# Google Gemini (optional)
GOOGLE_API_KEY=your-google-api-key

# FAL.AI (optional)
FAL_API_KEY=your-fal-api-key

# Langfuse (optional)
LANGFUSE_PUBLIC_KEY=your-langfuse-public-key
LANGFUSE_SECRET_KEY=your-langfuse-secret-key

# Redis (optional)
REDIS_URL=redis://localhost:6379

# Database (optional)
DATABASE_URL=sqlite:///./shoshin_ai.db
