"""
<PERSON><PERSON><PERSON> to run the Shoshin AI FastAPI server.
"""

import uvicorn
import os
import sys

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.core.config import get_settings

def main():
    """Run the FastAPI server."""
    settings = get_settings()
    
    print(f"🚀 Starting {settings.APP_NAME} server...")
    print(f"📍 Environment: {settings.ENVIRONMENT}")
    print(f"🌐 Server will be available at: http://{settings.HOST}:{settings.PORT}")
    print(f"📚 API Documentation: http://{settings.HOST}:{settings.PORT}/docs")
    print(f"❤️  Health Check: http://{settings.HOST}:{settings.PORT}/health")
    
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info" if not settings.DEBUG else "debug"
    )

if __name__ == "__main__":
    main()
